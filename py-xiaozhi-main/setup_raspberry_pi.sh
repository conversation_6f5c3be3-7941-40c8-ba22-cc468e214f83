#!/bin/bash

# 树莓派电机控制环境设置脚本
# 用于安装和配置L298N电机控制所需的依赖

echo "=== 树莓派电机控制环境设置 ==="

# 检查是否在树莓派上运行
if ! grep -q "Raspberry Pi" /proc/cpuinfo 2>/dev/null; then
    echo "警告: 此脚本设计用于树莓派，当前系统可能不是树莓派"
    read -p "是否继续? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 更新系统包
echo "更新系统包..."
sudo apt update

# 安装Python GPIO库
echo "安装RPi.GPIO库..."
pip3 install RPi.GPIO

# 检查GPIO权限
echo "检查GPIO权限..."
if ! groups $USER | grep -q gpio; then
    echo "将用户添加到gpio组..."
    sudo usermod -a -G gpio $USER
    echo "请注销并重新登录以使权限生效"
fi

# 创建GPIO测试脚本
cat > gpio_test.py << 'EOF'
#!/usr/bin/env python3
"""
GPIO连接测试脚本
测试L298N模块的GPIO连接
"""

import time
try:
    import RPi.GPIO as GPIO
    
    # GPIO引脚定义 (与motor_controller.py保持一致)
    ENA = 17  # 电机A使能引脚
    ENB = 18  # 电机B使能引脚  
    IN1 = 27  # 电机A方向控制1
    IN2 = 22  # 电机A方向控制2
    IN3 = 23  # 电机B方向控制1
    IN4 = 24  # 电机B方向控制2
    
    def test_gpio():
        print("开始GPIO连接测试...")
        
        # 设置GPIO模式
        GPIO.setmode(GPIO.BCM)
        GPIO.setwarnings(False)
        
        # 设置引脚为输出模式
        pins = [ENA, ENB, IN1, IN2, IN3, IN4]
        for pin in pins:
            GPIO.setup(pin, GPIO.OUT)
            print(f"GPIO {pin} 设置为输出模式")
        
        # 测试每个引脚
        print("\n测试引脚输出...")
        for pin in pins:
            print(f"测试GPIO {pin}...")
            GPIO.output(pin, GPIO.HIGH)
            time.sleep(0.5)
            GPIO.output(pin, GPIO.LOW)
            time.sleep(0.5)
        
        print("\n测试PWM功能...")
        pwm_a = GPIO.PWM(ENA, 1000)
        pwm_b = GPIO.PWM(ENB, 1000)
        
        pwm_a.start(0)
        pwm_b.start(0)
        
        for duty in [25, 50, 75, 100]:
            print(f"PWM占空比: {duty}%")
            pwm_a.ChangeDutyCycle(duty)
            pwm_b.ChangeDutyCycle(duty)
            time.sleep(1)
        
        pwm_a.stop()
        pwm_b.stop()
        GPIO.cleanup()
        
        print("GPIO测试完成!")
    
    if __name__ == "__main__":
        test_gpio()
        
except ImportError:
    print("错误: 无法导入RPi.GPIO库")
    print("请运行: pip3 install RPi.GPIO")
except Exception as e:
    print(f"测试失败: {e}")
    GPIO.cleanup()
EOF

chmod +x gpio_test.py

# 创建L298N接线图说明
cat > L298N_wiring.txt << 'EOF'
=== L298N电机驱动模块接线说明 ===

树莓派5 GPIO连接:
- ENA (使能A) -> GPIO 17
- ENB (使能B) -> GPIO 18  
- IN1 (输入1) -> GPIO 27
- IN2 (输入2) -> GPIO 22
- IN3 (输入3) -> GPIO 23
- IN4 (输入4) -> GPIO 24
- GND -> 树莓派GND
- VCC -> 树莓派5V (或外部5V电源)

L298N模块连接:
- OUT1, OUT2 -> 电机A
- OUT3, OUT4 -> 电机B
- 12V -> 外部电源正极 (电机电源)
- GND -> 外部电源负极和树莓派GND共地

注意事项:
1. 确保L298N模块的VCC连接到5V电源
2. 电机电源(12V)与逻辑电源(5V)分离
3. 所有GND必须共地
4. 如果电机功率较大，建议使用外部电源
5. 检查跳线帽设置，确保ENA和ENB引脚可控制

测试步骤:
1. 运行 python3 gpio_test.py 测试GPIO连接
2. 运行 python3 test_motor_control.py 测试电机控制
3. 启动主程序进行语音控制测试
EOF

echo ""
echo "=== 安装完成 ==="
echo "1. GPIO测试脚本: gpio_test.py"
echo "2. 接线说明: L298N_wiring.txt"
echo "3. 电机控制测试: python3 test_motor_control.py"
echo ""
echo "请查看 L298N_wiring.txt 了解接线方法"
echo "运行 python3 gpio_test.py 测试GPIO连接"
echo ""
echo "如果用户被添加到gpio组，请注销并重新登录"
