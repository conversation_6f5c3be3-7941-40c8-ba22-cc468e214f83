import asyncio
import logging
from typing import Dict, Any

try:
    import RPi.GPIO as GPIO
except ImportError:
    # 在非树莓派环境下使用模拟模块
    class MockGPIO:
        BCM = "BCM"
        OUT = "OUT"
        HIGH = 1
        LOW = 0
        
        @staticmethod
        def setmode(mode):
            pass
        
        @staticmethod
        def setup(pin, mode):
            pass
        
        @staticmethod
        def output(pin, value):
            print(f"GPIO {pin} set to {value}")
        
        @staticmethod
        def PWM(pin, frequency):
            return MockPWM(pin, frequency)
        
        @staticmethod
        def cleanup():
            pass
    
    class MockPWM:
        def __init__(self, pin, frequency):
            self.pin = pin
            self.frequency = frequency
            self.duty_cycle = 0
        
        def start(self, duty_cycle):
            self.duty_cycle = duty_cycle
            print(f"PWM {self.pin} started with duty cycle {duty_cycle}%")
        
        def ChangeDutyCycle(self, duty_cycle):
            self.duty_cycle = duty_cycle
            print(f"PWM {self.pin} duty cycle changed to {duty_cycle}%")
        
        def stop(self):
            print(f"PWM {self.pin} stopped")
    
    GPIO = MockGPIO()

from src.iot.thing import Thing, Parameter, ValueType
from src.utils.logging_config import get_logger

logger = get_logger(__name__)


class MotorController(Thing):
    """L298N电机控制器
    
    支持控制两个直流电机，包括方向和速度控制
    GPIO连接：ENA=17, ENB=18, IN1=27, IN2=22, IN3=23, IN4=24
    """
    
    def __init__(self):
        super().__init__("MotorController", "L298N双电机控制器")
        
        # GPIO引脚定义
        self.ENA = 17  # 电机A使能引脚
        self.ENB = 18  # 电机B使能引脚
        self.IN1 = 27  # 电机A方向控制1
        self.IN2 = 22  # 电机A方向控制2
        self.IN3 = 23  # 电机B方向控制1
        self.IN4 = 24  # 电机B方向控制2
        
        # 电机状态
        self.motor_a_speed = 0  # 0-100
        self.motor_b_speed = 0  # 0-100
        self.motor_a_direction = "stop"  # forward, backward, stop
        self.motor_b_direction = "stop"  # forward, backward, stop
        self.is_initialized = False
        
        # PWM对象
        self.pwm_a = None
        self.pwm_b = None
        
        # 初始化GPIO
        self._initialize_gpio()
        
        # 添加属性
        self.add_property("motor_a_speed", "电机A速度(0-100)", self.get_motor_a_speed)
        self.add_property("motor_b_speed", "电机B速度(0-100)", self.get_motor_b_speed)
        self.add_property("motor_a_direction", "电机A方向", self.get_motor_a_direction)
        self.add_property("motor_b_direction", "电机B方向", self.get_motor_b_direction)
        self.add_property("is_running", "是否有电机在运行", self.get_is_running)
        
        # 添加方法
        self.add_method(
            "ControlMotorA",
            "控制电机A",
            [
                Parameter("direction", "方向: forward/backward/stop", ValueType.STRING, True),
                Parameter("speed", "速度(0-100)", ValueType.NUMBER, False)
            ],
            self._control_motor_a
        )
        
        self.add_method(
            "ControlMotorB", 
            "控制电机B",
            [
                Parameter("direction", "方向: forward/backward/stop", ValueType.STRING, True),
                Parameter("speed", "速度(0-100)", ValueType.NUMBER, False)
            ],
            self._control_motor_b
        )
        
        self.add_method(
            "ControlBothMotors",
            "同时控制两个电机",
            [
                Parameter("direction_a", "电机A方向: forward/backward/stop", ValueType.STRING, True),
                Parameter("speed_a", "电机A速度(0-100)", ValueType.NUMBER, False),
                Parameter("direction_b", "电机B方向: forward/backward/stop", ValueType.STRING, True),
                Parameter("speed_b", "电机B速度(0-100)", ValueType.NUMBER, False)
            ],
            self._control_both_motors
        )
        
        self.add_method(
            "StopAllMotors",
            "停止所有电机",
            [],
            self._stop_all_motors
        )
        
        self.add_method(
            "MoveForward",
            "向前移动",
            [Parameter("speed", "速度(0-100)", ValueType.NUMBER, False)],
            self._move_forward
        )
        
        self.add_method(
            "MoveBackward", 
            "向后移动",
            [Parameter("speed", "速度(0-100)", ValueType.NUMBER, False)],
            self._move_backward
        )
        
        self.add_method(
            "TurnLeft",
            "向左转",
            [Parameter("speed", "速度(0-100)", ValueType.NUMBER, False)],
            self._turn_left
        )
        
        self.add_method(
            "TurnRight",
            "向右转", 
            [Parameter("speed", "速度(0-100)", ValueType.NUMBER, False)],
            self._turn_right
        )
    
    def _initialize_gpio(self):
        """初始化GPIO设置"""
        try:
            GPIO.setmode(GPIO.BCM)
            
            # 设置引脚模式
            GPIO.setup(self.ENA, GPIO.OUT)
            GPIO.setup(self.ENB, GPIO.OUT)
            GPIO.setup(self.IN1, GPIO.OUT)
            GPIO.setup(self.IN2, GPIO.OUT)
            GPIO.setup(self.IN3, GPIO.OUT)
            GPIO.setup(self.IN4, GPIO.OUT)
            
            # 初始化PWM
            self.pwm_a = GPIO.PWM(self.ENA, 1000)  # 1kHz频率
            self.pwm_b = GPIO.PWM(self.ENB, 1000)  # 1kHz频率
            
            self.pwm_a.start(0)
            self.pwm_b.start(0)
            
            # 初始状态：停止
            self._set_motor_direction("A", "stop")
            self._set_motor_direction("B", "stop")
            
            self.is_initialized = True
            logger.info("电机控制器GPIO初始化成功")
            
        except Exception as e:
            logger.error(f"GPIO初始化失败: {e}")
            self.is_initialized = False
    
    def _set_motor_direction(self, motor: str, direction: str):
        """设置电机方向"""
        if not self.is_initialized:
            return
            
        if motor == "A":
            if direction == "forward":
                GPIO.output(self.IN1, GPIO.HIGH)
                GPIO.output(self.IN2, GPIO.LOW)
            elif direction == "backward":
                GPIO.output(self.IN1, GPIO.LOW)
                GPIO.output(self.IN2, GPIO.HIGH)
            else:  # stop
                GPIO.output(self.IN1, GPIO.LOW)
                GPIO.output(self.IN2, GPIO.LOW)
        elif motor == "B":
            if direction == "forward":
                GPIO.output(self.IN3, GPIO.HIGH)
                GPIO.output(self.IN4, GPIO.LOW)
            elif direction == "backward":
                GPIO.output(self.IN3, GPIO.LOW)
                GPIO.output(self.IN4, GPIO.HIGH)
            else:  # stop
                GPIO.output(self.IN3, GPIO.LOW)
                GPIO.output(self.IN4, GPIO.LOW)
    
    def _set_motor_speed(self, motor: str, speed: int):
        """设置电机速度"""
        if not self.is_initialized:
            return
            
        speed = max(0, min(100, speed))  # 限制在0-100范围
        
        if motor == "A":
            self.pwm_a.ChangeDutyCycle(speed)
        elif motor == "B":
            self.pwm_b.ChangeDutyCycle(speed)
    
    # 属性获取方法
    async def get_motor_a_speed(self):
        return self.motor_a_speed
    
    async def get_motor_b_speed(self):
        return self.motor_b_speed
    
    async def get_motor_a_direction(self):
        return self.motor_a_direction
    
    async def get_motor_b_direction(self):
        return self.motor_b_direction
    
    async def get_is_running(self):
        return self.motor_a_speed > 0 or self.motor_b_speed > 0
    
    # 控制方法
    async def _control_motor_a(self, params: Dict[str, Parameter]) -> Dict[str, Any]:
        """控制电机A"""
        direction = params["direction"].get_value()
        speed = params.get("speed", Parameter("speed", "", ValueType.NUMBER)).get_value() or 50
        
        if direction not in ["forward", "backward", "stop"]:
            return {"status": "error", "message": "无效的方向参数"}
        
        self.motor_a_direction = direction
        if direction == "stop":
            self.motor_a_speed = 0
        else:
            self.motor_a_speed = max(0, min(100, int(speed)))
        
        self._set_motor_direction("A", direction)
        self._set_motor_speed("A", self.motor_a_speed)
        
        return {
            "status": "success", 
            "message": f"电机A设置为{direction}，速度{self.motor_a_speed}"
        }
    
    async def _control_motor_b(self, params: Dict[str, Parameter]) -> Dict[str, Any]:
        """控制电机B"""
        direction = params["direction"].get_value()
        speed = params.get("speed", Parameter("speed", "", ValueType.NUMBER)).get_value() or 50
        
        if direction not in ["forward", "backward", "stop"]:
            return {"status": "error", "message": "无效的方向参数"}
        
        self.motor_b_direction = direction
        if direction == "stop":
            self.motor_b_speed = 0
        else:
            self.motor_b_speed = max(0, min(100, int(speed)))
        
        self._set_motor_direction("B", direction)
        self._set_motor_speed("B", self.motor_b_speed)
        
        return {
            "status": "success",
            "message": f"电机B设置为{direction}，速度{self.motor_b_speed}"
        }
    
    async def _control_both_motors(self, params: Dict[str, Parameter]) -> Dict[str, Any]:
        """同时控制两个电机"""
        direction_a = params["direction_a"].get_value()
        speed_a = params.get("speed_a", Parameter("speed_a", "", ValueType.NUMBER)).get_value() or 50
        direction_b = params["direction_b"].get_value()
        speed_b = params.get("speed_b", Parameter("speed_b", "", ValueType.NUMBER)).get_value() or 50
        
        # 控制电机A
        await self._control_motor_a({
            "direction": Parameter("direction", "", ValueType.STRING),
            "speed": Parameter("speed", "", ValueType.NUMBER)
        })
        params["direction"].set_value(direction_a)
        params["speed"].set_value(speed_a)
        
        # 控制电机B  
        await self._control_motor_b({
            "direction": Parameter("direction", "", ValueType.STRING),
            "speed": Parameter("speed", "", ValueType.NUMBER)
        })
        params["direction"].set_value(direction_b)
        params["speed"].set_value(speed_b)
        
        return {
            "status": "success",
            "message": f"电机A: {direction_a}({speed_a}), 电机B: {direction_b}({speed_b})"
        }
    
    async def _stop_all_motors(self, params: Dict[str, Parameter]) -> Dict[str, Any]:
        """停止所有电机"""
        self.motor_a_direction = "stop"
        self.motor_b_direction = "stop"
        self.motor_a_speed = 0
        self.motor_b_speed = 0
        
        self._set_motor_direction("A", "stop")
        self._set_motor_direction("B", "stop")
        self._set_motor_speed("A", 0)
        self._set_motor_speed("B", 0)
        
        return {"status": "success", "message": "所有电机已停止"}
    
    async def _move_forward(self, params: Dict[str, Parameter]) -> Dict[str, Any]:
        """向前移动"""
        speed = params.get("speed", Parameter("speed", "", ValueType.NUMBER)).get_value() or 50
        
        self.motor_a_direction = "forward"
        self.motor_b_direction = "forward"
        self.motor_a_speed = speed
        self.motor_b_speed = speed
        
        self._set_motor_direction("A", "forward")
        self._set_motor_direction("B", "forward")
        self._set_motor_speed("A", speed)
        self._set_motor_speed("B", speed)
        
        return {"status": "success", "message": f"向前移动，速度{speed}"}
    
    async def _move_backward(self, params: Dict[str, Parameter]) -> Dict[str, Any]:
        """向后移动"""
        speed = params.get("speed", Parameter("speed", "", ValueType.NUMBER)).get_value() or 50
        
        self.motor_a_direction = "backward"
        self.motor_b_direction = "backward"
        self.motor_a_speed = speed
        self.motor_b_speed = speed
        
        self._set_motor_direction("A", "backward")
        self._set_motor_direction("B", "backward")
        self._set_motor_speed("A", speed)
        self._set_motor_speed("B", speed)
        
        return {"status": "success", "message": f"向后移动，速度{speed}"}
    
    async def _turn_left(self, params: Dict[str, Parameter]) -> Dict[str, Any]:
        """向左转"""
        speed = params.get("speed", Parameter("speed", "", ValueType.NUMBER)).get_value() or 50
        
        self.motor_a_direction = "backward"  # 左轮后退
        self.motor_b_direction = "forward"   # 右轮前进
        self.motor_a_speed = speed
        self.motor_b_speed = speed
        
        self._set_motor_direction("A", "backward")
        self._set_motor_direction("B", "forward")
        self._set_motor_speed("A", speed)
        self._set_motor_speed("B", speed)
        
        return {"status": "success", "message": f"向左转，速度{speed}"}
    
    async def _turn_right(self, params: Dict[str, Parameter]) -> Dict[str, Any]:
        """向右转"""
        speed = params.get("speed", Parameter("speed", "", ValueType.NUMBER)).get_value() or 50
        
        self.motor_a_direction = "forward"   # 左轮前进
        self.motor_b_direction = "backward"  # 右轮后退
        self.motor_a_speed = speed
        self.motor_b_speed = speed
        
        self._set_motor_direction("A", "forward")
        self._set_motor_direction("B", "backward")
        self._set_motor_speed("A", speed)
        self._set_motor_speed("B", speed)
        
        return {"status": "success", "message": f"向右转，速度{speed}"}
    
    def __del__(self):
        """析构函数，清理GPIO"""
        if self.is_initialized:
            try:
                if self.pwm_a:
                    self.pwm_a.stop()
                if self.pwm_b:
                    self.pwm_b.stop()
                GPIO.cleanup()
                logger.info("GPIO资源已清理")
            except Exception as e:
                logger.error(f"GPIO清理失败: {e}")
