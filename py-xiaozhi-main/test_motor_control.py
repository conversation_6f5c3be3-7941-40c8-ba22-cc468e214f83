#!/usr/bin/env python3
"""
电机控制测试脚本
用于测试L298N电机控制器的功能
"""

import asyncio
import json
from src.iot.thing_manager import ThingManager
from src.utils.logging_config import setup_logging, get_logger

# 设置日志
setup_logging()
logger = get_logger(__name__)


async def test_motor_control():
    """测试电机控制功能"""
    print("=== 电机控制测试 ===")
    
    # 获取设备管理器
    thing_manager = ThingManager.get_instance()
    
    # 初始化IoT设备
    await thing_manager.initialize_iot_devices(None)
    
    # 获取设备描述符
    descriptors = await thing_manager.get_descriptors_json()
    print(f"\n设备描述符:\n{json.dumps(json.loads(descriptors), indent=2, ensure_ascii=False)}")
    
    # 获取初始状态
    states = await thing_manager.get_states_json_str()
    print(f"\n初始状态:\n{json.dumps(json.loads(states), indent=2, ensure_ascii=False)}")
    
    # 测试命令列表
    test_commands = [
        {
            "name": "MotorController",
            "method": "MoveForward",
            "parameters": {"speed": 30}
        },
        {
            "name": "MotorController", 
            "method": "StopAllMotors",
            "parameters": {}
        },
        {
            "name": "MotorController",
            "method": "TurnLeft",
            "parameters": {"speed": 40}
        },
        {
            "name": "MotorController",
            "method": "StopAllMotors", 
            "parameters": {}
        },
        {
            "name": "MotorController",
            "method": "MoveBackward",
            "parameters": {"speed": 25}
        },
        {
            "name": "MotorController",
            "method": "StopAllMotors",
            "parameters": {}
        },
        {
            "name": "MotorController",
            "method": "ControlMotorA",
            "parameters": {"direction": "forward", "speed": 60}
        },
        {
            "name": "MotorController",
            "method": "ControlMotorB", 
            "parameters": {"direction": "backward", "speed": 40}
        },
        {
            "name": "MotorController",
            "method": "StopAllMotors",
            "parameters": {}
        }
    ]
    
    # 执行测试命令
    for i, command in enumerate(test_commands):
        print(f"\n--- 测试 {i+1}: {command['method']} ---")
        print(f"命令: {json.dumps(command, ensure_ascii=False)}")
        
        try:
            result = await thing_manager.invoke(command)
            print(f"结果: {json.dumps(result, ensure_ascii=False)}")
            
            # 获取状态更新
            states = await thing_manager.get_states_json_str()
            motor_state = json.loads(states)[0]["state"]
            print(f"电机状态: {json.dumps(motor_state, ensure_ascii=False)}")
            
        except Exception as e:
            print(f"错误: {e}")
        
        # 等待一段时间观察效果
        await asyncio.sleep(2)
    
    print("\n=== 测试完成 ===")


async def interactive_test():
    """交互式测试"""
    print("=== 交互式电机控制测试 ===")
    print("可用命令:")
    print("1. forward [speed] - 向前移动")
    print("2. backward [speed] - 向后移动") 
    print("3. left [speed] - 向左转")
    print("4. right [speed] - 向右转")
    print("5. stop - 停止所有电机")
    print("6. status - 查看状态")
    print("7. quit - 退出")
    
    # 获取设备管理器
    thing_manager = ThingManager.get_instance()
    await thing_manager.initialize_iot_devices(None)
    
    while True:
        try:
            user_input = input("\n请输入命令: ").strip().split()
            if not user_input:
                continue
                
            command = user_input[0].lower()
            speed = int(user_input[1]) if len(user_input) > 1 else 50
            
            if command == "quit":
                break
            elif command == "status":
                states = await thing_manager.get_states_json_str()
                motor_state = json.loads(states)[0]["state"]
                print(f"电机状态: {json.dumps(motor_state, indent=2, ensure_ascii=False)}")
                continue
            
            # 构建IoT命令
            iot_command = {"name": "MotorController", "parameters": {}}
            
            if command == "forward":
                iot_command["method"] = "MoveForward"
                iot_command["parameters"]["speed"] = speed
            elif command == "backward":
                iot_command["method"] = "MoveBackward"
                iot_command["parameters"]["speed"] = speed
            elif command == "left":
                iot_command["method"] = "TurnLeft"
                iot_command["parameters"]["speed"] = speed
            elif command == "right":
                iot_command["method"] = "TurnRight"
                iot_command["parameters"]["speed"] = speed
            elif command == "stop":
                iot_command["method"] = "StopAllMotors"
            else:
                print("未知命令")
                continue
            
            # 执行命令
            result = await thing_manager.invoke(iot_command)
            print(f"执行结果: {result['message']}")
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"错误: {e}")
    
    # 确保退出时停止所有电机
    try:
        await thing_manager.invoke({
            "name": "MotorController",
            "method": "StopAllMotors", 
            "parameters": {}
        })
        print("已停止所有电机")
    except:
        pass


async def main():
    """主函数"""
    print("选择测试模式:")
    print("1. 自动测试")
    print("2. 交互式测试")
    
    try:
        choice = input("请选择 (1/2): ").strip()
        
        if choice == "1":
            await test_motor_control()
        elif choice == "2":
            await interactive_test()
        else:
            print("无效选择")
    except KeyboardInterrupt:
        print("\n测试被中断")
    except Exception as e:
        print(f"测试失败: {e}")


if __name__ == "__main__":
    asyncio.run(main())
